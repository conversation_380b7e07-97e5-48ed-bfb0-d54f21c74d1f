<script lang="ts">
	import type { Writable } from 'svelte/store';

	interface Props {
		sections: string[];
		sectionNames: string[];
		activeSection: Writable<string>;
		scrollToSection: (id: string) => void;
	}

	let { sections, sectionNames, activeSection, scrollToSection }: Props = $props();
</script>

<nav class="fixed top-1/2 -translate-y-1/2 left-8 hidden lg:flex flex-col gap-4 z-50">
	<ul class="flex flex-col gap-4">
		{#each sections as sectionId, i (sectionId)}
			<li class="relative group">
				<button
					onclick={() => scrollToSection(sectionId)}
					class="block w-3 h-3 rounded-full transition-all duration-300 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 backdrop-blur-md border shadow-lg {sectionId === $activeSection ? 'bg-white/20 border-white/30 h-5' : 'bg-white/10 border-white/20 hover:bg-white/15'}"
					aria-label="Go to {sectionNames[i]} section"
				></button>
				<!-- Tooltip -->
				<div class="absolute left-8 top-1/2 -translate-y-1/2 bg-white/10 backdrop-blur-md border border-white/20 text-white px-3 py-1 rounded-md text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap shadow-lg">
					{sectionNames[i]}
					<!-- Arrow pointing to the dot -->
					<div class="absolute right-full top-1/2 -translate-y-1/2 border-4 border-transparent border-r-white/20"></div>
				</div>
			</li>
		{/each}
	</ul>
</nav>

<!-- Mobile navigation indicator -->
<div class="fixed bottom-8 left-1/2 -translate-x-1/2 lg:hidden z-50">
	<div class="flex gap-2 bg-white/10 backdrop-blur-md border border-white/20 rounded-full px-4 py-2 shadow-lg">
		{#each sections as sectionId, i (sectionId)}
			<button
				onclick={() => scrollToSection(sectionId)}
				class="w-2 h-2 rounded-full transition-all duration-300 backdrop-blur-md border shadow-sm {sectionId === $activeSection ? 'bg-white/20 border-white/30 w-6' : 'bg-white/10 border-white/20'}"
				aria-label="Go to {sectionNames[i]} section"
			></button>
		{/each}
	</div>
</div>
