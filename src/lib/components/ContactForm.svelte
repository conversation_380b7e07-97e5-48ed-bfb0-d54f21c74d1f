<script lang="ts">
	import type { ContactFormLabels } from '$lib/content';

	interface Props {
		labels: ContactFormLabels;
	}

	let { labels }: Props = $props();

	// Form state
	let formData = $state({
		name: '',
		email: '',
		company: '',
		message: ''
	});

	let isSubmitting = $state(false);
	let submitStatus = $state<'idle' | 'success' | 'error' | 'network-error' | 'server-error'>('idle');

	// Field-level validation errors
	let fieldErrors = $state({
		name: '',
		email: '',
		message: ''
	});

	// Form validation
	function validateForm(): boolean {
		return !!(
			formData.name.trim() &&
			formData.email.trim() &&
			isValidEmail(formData.email) &&
			formData.message.trim()
		);
	}

	// Email validation helper
	function isValidEmail(email: string): boolean {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailRegex.test(email);
	}

	// Validate individual fields
	function validateField(field: 'name' | 'email' | 'message') {
		switch (field) {
			case 'name':
				fieldErrors.name = formData.name.trim() ? '' : labels.nameError;
				break;
			case 'email':
				if (!formData.email.trim()) {
					fieldErrors.email = labels.emailError;
				} else if (!isValidEmail(formData.email)) {
					fieldErrors.email = labels.emailInvalidError;
				} else {
					fieldErrors.email = '';
				}
				break;
			case 'message':
				fieldErrors.message = formData.message.trim() ? '' : labels.messageError;
				break;
		}
	}

	// Handle form submission
	async function handleSubmit(event: Event) {
		event.preventDefault();

		// Clear previous field errors
		fieldErrors = { name: '', email: '', message: '' };

		// Validate all fields
		validateField('name');
		validateField('email');
		validateField('message');

		// Check if form is valid
		if (!validateForm()) {
			return;
		}

		isSubmitting = true;
		submitStatus = 'idle';

		try {
			// Simulate API call - replace with actual endpoint when ready
			await new Promise((resolve, reject) => {
				setTimeout(() => {
					// Simulate random success/failure for testing
					const shouldSucceed = Math.random() > 0.1; // 90% success rate
					if (shouldSucceed) {
						resolve(true);
					} else {
						reject(new Error('Simulated server error'));
					}
				}, 1000);
			});

			console.log('Form submitted:', formData);
			submitStatus = 'success';

			// Reset form after successful submission
			formData = {
				name: '',
				email: '',
				company: '',
				message: ''
			};
			fieldErrors = { name: '', email: '', message: '' };
		} catch (error) {
			console.error('Form submission error:', error);
			// Check if it's a network error
			if (error instanceof TypeError && error.message.includes('fetch')) {
				submitStatus = 'network-error';
			} else {
				submitStatus = 'error';
			}
		} finally {
			isSubmitting = false;
		}
	}
</script>

<form onsubmit={handleSubmit} class="mx-auto max-w-lg space-y-6">
	<!-- Name Field -->
	<div>
		<label for="name" class="mb-2 block text-sm font-medium text-white drop-shadow-md">
			{labels.name} <span class="text-red-400">*</span>
		</label>
		<input
			type="text"
			id="name"
			bind:value={formData.name}
			onblur={() => validateField('name')}
			placeholder={labels.namePlaceholder}
			required
			class="w-full rounded-lg backdrop-blur-md px-4 py-3 text-white placeholder-white/60 transition-all duration-300 focus:bg-white/15 focus:ring-2 focus:outline-none {fieldErrors.name ? 'bg-red-500/10 border-red-400/50 focus:border-red-400/70 focus:ring-red-400/30' : 'bg-white/10 border-white/20 focus:border-white/40 focus:ring-white/30'}"
		/>
		{#if fieldErrors.name}
			<p class="mt-1 text-sm text-red-300 drop-shadow-md">{fieldErrors.name}</p>
		{/if}
	</div>

	<!-- Email Field -->
	<div>
		<label for="email" class="mb-2 block text-sm font-medium text-white drop-shadow-md">
			{labels.email} <span class="text-red-400">*</span>
		</label>
		<input
			type="email"
			id="email"
			bind:value={formData.email}
			onblur={() => validateField('email')}
			placeholder={labels.emailPlaceholder}
			required
			class="w-full rounded-lg backdrop-blur-md px-4 py-3 text-white placeholder-white/60 transition-all duration-300 focus:bg-white/15 focus:ring-2 focus:outline-none {fieldErrors.email ? 'bg-red-500/10 border-red-400/50 focus:border-red-400/70 focus:ring-red-400/30' : 'bg-white/10 border-white/20 focus:border-white/40 focus:ring-white/30'}"
		/>
		{#if fieldErrors.email}
			<p class="mt-1 text-sm text-red-300 drop-shadow-md">{fieldErrors.email}</p>
		{/if}
	</div>

	<!-- Company Field -->
	<div>
		<label for="company" class="mb-2 block text-sm font-medium text-white drop-shadow-md">
			{labels.company}
		</label>
		<input
			type="text"
			id="company"
			bind:value={formData.company}
			placeholder={labels.companyPlaceholder}
			class="w-full rounded-lg bg-white/10 backdrop-blur-md border border-white/20 px-4 py-3 text-white placeholder-white/60 transition-all duration-300 focus:bg-white/15 focus:border-white/40 focus:ring-2 focus:ring-white/30 focus:outline-none"
		/>
	</div>

	<!-- Message Field -->
	<div>
		<label for="message" class="mb-2 block text-sm font-medium text-white drop-shadow-md">
			{labels.message} <span class="text-red-400">*</span>
		</label>
		<textarea
			id="message"
			bind:value={formData.message}
			onblur={() => validateField('message')}
			placeholder={labels.messagePlaceholder}
			required
			rows="5"
			class="w-full resize-vertical rounded-lg backdrop-blur-md px-4 py-3 text-white placeholder-white/60 transition-all duration-300 focus:bg-white/15 focus:ring-2 focus:outline-none {fieldErrors.message ? 'bg-red-500/10 border-red-400/50 focus:border-red-400/70 focus:ring-red-400/30' : 'bg-white/10 border-white/20 focus:border-white/40 focus:ring-white/30'}"
		></textarea>
		{#if fieldErrors.message}
			<p class="mt-1 text-sm text-red-300 drop-shadow-md">{fieldErrors.message}</p>
		{/if}
	</div>

	<!-- Submit Button -->
	<button
		type="submit"
		disabled={!validateForm() || isSubmitting}
		class="flex w-full items-center justify-center gap-2 rounded-lg bg-white/20 backdrop-blur-md border border-white/30 px-8 py-4 font-bold text-white transition-all duration-300 hover:bg-white/30 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-white/50 disabled:cursor-not-allowed disabled:bg-white/10 disabled:text-white/50 disabled:hover:scale-100"
	>
		{#if isSubmitting}
			<svg class="h-5 w-5 animate-spin" viewBox="0 0 24 24">
				<circle
					class="opacity-25"
					cx="12"
					cy="12"
					r="10"
					stroke="currentColor"
					stroke-width="4"
					fill="none"
				></circle>
				<path
					class="opacity-75"
					fill="currentColor"
					d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
				></path>
			</svg>
			Sending...
		{:else}
			{labels.submit}
		{/if}
	</button>

	<!-- Status Messages -->
	{#if submitStatus === 'success'}
		<div class="rounded-lg bg-green-500/20 backdrop-blur-md border border-green-400/30 p-4 shadow-lg">
			<div class="flex items-center gap-2">
				<svg class="h-5 w-5 text-green-300" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
				</svg>
				<p class="text-sm font-medium text-green-100 drop-shadow-md">{labels.successMessage}</p>
			</div>
		</div>
	{/if}

	{#if submitStatus === 'network-error'}
		<div class="rounded-lg bg-red-500/20 backdrop-blur-md border border-red-400/30 p-4 shadow-lg">
			<div class="flex items-center gap-2">
				<svg class="h-5 w-5 text-red-300" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
				</svg>
				<p class="text-sm font-medium text-red-100 drop-shadow-md">{labels.networkError}</p>
			</div>
		</div>
	{/if}

	{#if submitStatus === 'server-error'}
		<div class="rounded-lg bg-red-500/20 backdrop-blur-md border border-red-400/30 p-4 shadow-lg">
			<div class="flex items-center gap-2">
				<svg class="h-5 w-5 text-red-300" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
				</svg>
				<p class="text-sm font-medium text-red-100 drop-shadow-md">{labels.serverError}</p>
			</div>
		</div>
	{/if}

	{#if submitStatus === 'error'}
		<div class="rounded-lg bg-red-500/20 backdrop-blur-md border border-red-400/30 p-4 shadow-lg">
			<div class="flex items-center gap-2">
				<svg class="h-5 w-5 text-red-300" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
				</svg>
				<p class="text-sm font-medium text-red-100 drop-shadow-md">{labels.errorMessage}</p>
			</div>
		</div>
	{/if}
</form>
