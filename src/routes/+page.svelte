<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { writable } from 'svelte/store';
	import Logo from '$lib/Logo.svelte';
	import { getCurrentContent } from '$lib/content';
	import NavigationDots from '$lib/components/NavigationDots.svelte';
	import ContactForm from '$lib/components/ContactForm.svelte';

	// Get current content
	const content = getCurrentContent();

	// GSAP will be imported dynamically in onMount for better performance
	let gsap: any;
	let ScrollToPlugin: any;
	let Observer: any;

	// Full-page scroll state
	const sectionIds = ['hero', 'philosophy', 'services', 'contact'];
	const sectionNames = content.navigation.sectionNames;
	let currentSectionIndex = 0;
	let isAnimating = false;
	const activeSection = writable(sectionIds[0]);

	// Reactive state for Services tabs (Svelte 5 syntax)
	let activeTab = $state('traditional');

	// Video background state
	let videoElement: HTMLVideoElement;
	let isVideoPlaying = $state(true);
	let prefersReducedMotion = $state(false);

	// Store event handler references for cleanup
	let keydownHandler: ((e: KeyboardEvent) => void) | null = null;
	let scrollHandler: (() => void) | null = null;

	// Function to detect current section based on scroll position
	function updateCurrentSection() {
		if (isAnimating) return;

		const windowHeight = window.innerHeight;

		// Find which section is currently most visible
		let newSectionIndex = 0;
		let maxVisibility = 0;

		sectionIds.forEach((sectionId, index) => {
			const element = document.getElementById(sectionId);
			if (!element) return;

			const rect = element.getBoundingClientRect();
			const elementTop = rect.top;
			const elementBottom = rect.bottom;

			// Calculate how much of the section is visible
			const visibleTop = Math.max(0, -elementTop);
			const visibleBottom = Math.min(
				windowHeight,
				windowHeight - Math.max(0, elementBottom - windowHeight)
			);
			const visibleHeight = Math.max(0, visibleBottom - visibleTop);
			const visibility = visibleHeight / windowHeight;

			if (visibility > maxVisibility) {
				maxVisibility = visibility;
				newSectionIndex = index;
			}
		});

		if (newSectionIndex !== currentSectionIndex) {
			currentSectionIndex = newSectionIndex;
			activeSection.set(sectionIds[newSectionIndex]);
		}
	}

	async function scrollToSection(id: string) {
		const index = sectionIds.indexOf(id);
		if (index === -1 || isAnimating) return;

		const previousSectionId = sectionIds[currentSectionIndex];

		// Update current section index and active section
		isAnimating = true;
		activeSection.set(id);

		// Add scrolling class to prevent manual scrolling during animation
		document.body.classList.add('scrolling');

		// Kill any existing animations to prevent conflicts - comprehensive cleanup
		gsap.killTweensOf(window);

		// Kill all section animations
		gsap.killTweensOf('.hero-logo, .hero-headline, .hero-subheadline, .hero-cta');
		gsap.killTweensOf('.philosophy-heading, .philosophy-paragraph, .philosophy-visual');
		gsap.killTweensOf('.services-header, .services-tabs, .service-item, .tab-content');
		gsap.killTweensOf('.cta-heading, .cta-subheading, .contact-form');

		// Kill any running timelines
		gsap.globalTimeline.getChildren().forEach((tl: any) => {
			if (tl.kill) tl.kill();
		});

		// Play out animation for current section first
		if (previousSectionId !== id) {
			await playCurrentSectionOutAnimation(previousSectionId);
		}

		// Update current section index after out animation
		currentSectionIndex = index;

		// Scroll to new section
		gsap.to(window, {
			scrollTo: { y: `#${id}`, autoKill: false },
			duration: 1.2,
			ease: 'power3.inOut',
			onComplete: () => {
				// Small delay to ensure smooth transition
				gsap.delayedCall(0.2, () => {
					playContentAnimation(id);
				});
				isAnimating = false;
				// Remove scrolling class
				document.body.classList.remove('scrolling');
			}
		});
	}

	// Video control functions
	function toggleVideo() {
		if (!videoElement) return;

		if (isVideoPlaying) {
			videoElement.pause();
			isVideoPlaying = false;
		} else {
			videoElement.play();
			isVideoPlaying = true;
		}
	}

	function handleReducedMotion() {
		// Check for prefers-reduced-motion
		const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
		prefersReducedMotion = mediaQuery.matches;

		if (prefersReducedMotion && videoElement) {
			videoElement.pause();
			isVideoPlaying = false;
		}

		// Listen for changes
		mediaQuery.addEventListener('change', (e) => {
			prefersReducedMotion = e.matches;
			if (prefersReducedMotion && videoElement) {
				videoElement.pause();
				isVideoPlaying = false;
			}
		});
	}

	onMount(async () => {
		// Dynamic import of GSAP for better performance
		const gsapModule = await import('gsap');
		const scrollToPluginModule = await import('gsap/ScrollToPlugin');
		const observerModule = await import('gsap/Observer');

		gsap = gsapModule.gsap;
		ScrollToPlugin = scrollToPluginModule.ScrollToPlugin;
		Observer = observerModule.Observer;

		gsap.registerPlugin(ScrollToPlugin, Observer);

		// Initialize video accessibility
		handleReducedMotion();

		// Ensure page always starts at hero section on load/refresh
		gsap.set(window, { scrollTo: { y: '#hero', autoKill: false } });

		// Reset current section state
		currentSectionIndex = 0;
		activeSection.set('hero');

		// Initialize all section animations in reset state
		resetAllAnimations();

		// Play initial hero animation with a small delay
		gsap.delayedCall(0.1, () => {
			playHeroAnimation();
		});

		// Set up Observer for full-page scroll
		Observer.create({
			target: window,
			type: 'wheel,touch',
			wheelSpeed: -1,
			onUp: () => {
				if (currentSectionIndex < sectionIds.length - 1 && !isAnimating) {
					scrollToSection(sectionIds[currentSectionIndex + 1]);
				}
			},
			onDown: () => {
				if (currentSectionIndex > 0 && !isAnimating) {
					scrollToSection(sectionIds[currentSectionIndex - 1]);
				}
			},
			tolerance: 50, // Increased tolerance for better control
			preventDefault: true
		});

		// Add keyboard navigation
		keydownHandler = (e: KeyboardEvent) => {
			if (isAnimating) return;

			switch (e.key) {
				case 'ArrowUp':
				case 'PageUp':
					e.preventDefault();
					if (currentSectionIndex > 0) {
						scrollToSection(sectionIds[currentSectionIndex - 1]);
					}
					break;
				case 'ArrowDown':
				case 'PageDown':
				case ' ': // Spacebar
					e.preventDefault();
					if (currentSectionIndex < sectionIds.length - 1) {
						scrollToSection(sectionIds[currentSectionIndex + 1]);
					}
					break;
				case 'Home':
					e.preventDefault();
					scrollToSection(sectionIds[0]);
					break;
				case 'End':
					e.preventDefault();
					scrollToSection(sectionIds[sectionIds.length - 1]);
					break;
			}
		};

		window.addEventListener('keydown', keydownHandler);

		// Add scroll listener to update current section
		scrollHandler = () => {
			updateCurrentSection();
		};

		window.addEventListener('scroll', scrollHandler, { passive: true });

		// Initial section detection
		setTimeout(updateCurrentSection, 100);
	});

	function playContentAnimation(sectionId: string) {
		switch (sectionId) {
			case 'hero':
				playHeroAnimation();
				break;
			case 'philosophy':
				playPhilosophyAnimation();
				break;
			case 'services':
				playServicesAnimation();
				break;
			case 'contact':
				playContactAnimation();
				break;
		}
	}

	function resetAllAnimations() {
		if (!gsap) return;

		// Reset all sections to initial state with subtle positioning
		gsap.set('.hero-logo, .hero-headline, .hero-subheadline', { opacity: 0, y: 30 });
		gsap.set('.hero-cta', { opacity: 0, y: 30, scale: 0.95 });
		gsap.set('.philosophy-heading, .philosophy-paragraph, .philosophy-visual', {
			opacity: 0,
			y: 30
		});
		gsap.set('.services-header, .services-tabs', { opacity: 0, y: 30 });
		gsap.set('.service-item', { opacity: 0, y: -30 }); // Service items fade from top
		gsap.set('.cta-heading, .cta-subheading, .contact-form', { opacity: 0, y: 30 });
	}

	// Out animation functions for smooth transitions
	function playCurrentSectionOutAnimation(currentSection: string): Promise<void> {
		return new Promise((resolve) => {
			switch (currentSection) {
				case 'hero':
					playHeroOutAnimation(resolve);
					break;
				case 'philosophy':
					playPhilosophyOutAnimation(resolve);
					break;
				case 'services':
					playServicesOutAnimation(resolve);
					break;
				case 'contact':
					playContactOutAnimation(resolve);
					break;
				default:
					resolve();
			}
		});
	}

	function playHeroOutAnimation(onComplete: () => void) {
		if (!gsap) return onComplete();

		const tl = gsap.timeline({ onComplete });

		tl.to('.hero-cta', {
			duration: 0.4,
			opacity: 0,
			y: -15,
			scale: 0.95,
			ease: 'power2.inOut'
		})
			.to(
				'.hero-subheadline',
				{
					duration: 0.4,
					opacity: 0,
					y: -15,
					ease: 'power2.inOut'
				},
				'-=0.3'
			)
			.to(
				'.hero-headline',
				{
					duration: 0.4,
					opacity: 0,
					y: -15,
					ease: 'power2.inOut'
				},
				'-=0.3'
			)
			.to(
				'.hero-logo',
				{
					duration: 0.4,
					opacity: 0,
					y: -15,
					ease: 'power2.inOut'
				},
				'-=0.3'
			);
	}

	function playPhilosophyOutAnimation(onComplete: () => void) {
		if (!gsap) return onComplete();

		const tl = gsap.timeline({ onComplete });

		tl.to('.philosophy-visual', {
			duration: 0.5,
			opacity: 0,
			y: 15,
			scale: 0.95,
			ease: 'power2.inOut'
		})
			.to(
				'.philosophy-paragraph',
				{
					duration: 0.4,
					opacity: 0,
					y: -12,
					stagger: -0.08,
					ease: 'power2.inOut'
				},
				'-=0.4'
			)
			.to(
				'.philosophy-heading',
				{
					duration: 0.5,
					opacity: 0,
					y: -15,
					ease: 'power2.inOut'
				},
				'-=0.3'
			);
	}

	function playServicesOutAnimation(onComplete: () => void) {
		if (!gsap) return onComplete();

		const tl = gsap.timeline({ onComplete });

		tl.to('.service-item', {
			duration: 0.4,
			opacity: 0,
			y: 20,
			stagger: {
				amount: 0.3,
				from: 'end',
				grid: 'auto',
				axis: 'x'
			},
			ease: 'power2.inOut'
		})
			.to(
				'.services-tabs',
				{
					duration: 0.4,
					opacity: 0,
					y: -12,
					ease: 'power2.inOut'
				},
				'-=0.2'
			)
			.to(
				'.services-header',
				{
					duration: 0.5,
					opacity: 0,
					y: -15,
					ease: 'power2.inOut'
				},
				'-=0.3'
			);
	}

	function playContactOutAnimation(onComplete: () => void) {
		if (!gsap) return onComplete();

		const tl = gsap.timeline({ onComplete });

		tl.to('.contact-form', {
			duration: 0.5,
			opacity: 0,
			y: 15,
			ease: 'power2.inOut'
		})
			.to(
				'.cta-subheading',
				{
					duration: 0.4,
					opacity: 0,
					y: -12,
					ease: 'power2.inOut'
				},
				'-=0.4'
			)
			.to(
				'.cta-heading',
				{
					duration: 0.5,
					opacity: 0,
					y: -15,
					ease: 'power2.inOut'
				},
				'-=0.3'
			);
	}

	onDestroy(() => {
		// Clean up GSAP Observer instances
		if (Observer) {
			Observer.getAll().forEach((observer: any) => observer.kill());
		}

		// Remove event listeners
		if (keydownHandler) {
			window.removeEventListener('keydown', keydownHandler);
		}
		if (scrollHandler) {
			window.removeEventListener('scroll', scrollHandler);
		}
	});

	function switchTab(tab: 'traditional' | 'tech') {
		if (activeTab === tab || !gsap) return;

		// Kill any existing service item animations
		gsap.killTweensOf('.service-item');

		// Change the active tab immediately
		activeTab = tab;

		// Use requestAnimationFrame to ensure DOM has updated
		requestAnimationFrame(() => {
			// Reset new service items to hidden state
			gsap.set('.service-item', { opacity: 0, y: -30, clearProps: 'transform' });

			// Animate them in from left to right, top to bottom
			animateServiceItems();
		});
	}

	function playHeroAnimation() {
		if (!gsap) return;

		// Hero section load-in sequence - smooth and aesthetic
		const tl = gsap.timeline();

		tl.to('.hero-logo', {
			duration: 1,
			y: 0,
			opacity: 1,
			ease: 'power3.out'
		})
			.to(
				'.hero-headline',
				{
					duration: 1.2,
					y: 0,
					opacity: 1,
					ease: 'power3.out'
				},
				'-=0.6'
			)
			.to(
				'.hero-subheadline',
				{
					duration: 1,
					y: 0,
					opacity: 1,
					ease: 'power2.out'
				},
				'-=0.8'
			)
			.to(
				'.hero-cta',
				{
					duration: 1,
					y: 0,
					opacity: 1,
					scale: 1,
					ease: 'power3.out'
				},
				'-=0.5'
			);
	}

	function playPhilosophyAnimation() {
		if (!gsap) return;

		// Philosophy section animation sequence - smooth and elegant
		const tl = gsap.timeline();

		tl.to('.philosophy-heading', {
			duration: 1.2,
			y: 0,
			opacity: 1,
			ease: 'power3.out'
		})
			.to(
				'.philosophy-paragraph',
				{
					duration: 1,
					y: 0,
					opacity: 1,
					stagger: 0.15,
					ease: 'power2.out'
				},
				'-=0.8'
			)
			.to(
				'.philosophy-visual',
				{
					duration: 1.2,
					y: 0,
					opacity: 1,
					scale: 1,
					ease: 'power3.out'
				},
				'-=1'
			);
	}

	function playServicesAnimation() {
		if (!gsap) return;

		// Services section animation sequence - smooth and refined
		const tl = gsap.timeline();

		tl.to('.services-header', {
			duration: 1.2,
			y: 0,
			opacity: 1,
			ease: 'power3.out'
		})
			.to(
				'.services-tabs',
				{
					duration: 1,
					y: 0,
					opacity: 1,
					ease: 'power2.out'
				},
				'-=0.8'
			)
			.to(
				'.service-item',
				{
					duration: 0.8,
					y: 0,
					opacity: 1,
					stagger: {
						amount: 0.6,
						from: 'start',
						grid: 'auto',
						axis: 'x'
					},
					ease: 'power3.out'
				},
				'-=0.4'
			);
	}

	// Animate service items individually - used for tab switching
	function animateServiceItems() {
		if (!gsap) return;

		// Force reset service items first - ensure they're hidden
		gsap.killTweensOf('.service-item');
		gsap.set('.service-item', { opacity: 0, y: -30, clearProps: 'all' });

		// Small delay to ensure reset, then animate each item from left to right, top to bottom
		gsap.delayedCall(0.02, () => {
			gsap.to('.service-item', {
				duration: 0.8,
				y: 0,
				opacity: 1,
				stagger: {
					amount: 0.6,
					from: 'start',
					grid: 'auto',
					axis: 'x'
				},
				ease: 'power3.out'
			});
		});
	}

	function playContactAnimation() {
		if (!gsap) return;

		// Contact section animation sequence - smooth and elegant reveal
		const tl = gsap.timeline();

		tl.to('.cta-heading', {
			duration: 1.2,
			y: 0,
			opacity: 1,
			ease: 'power3.out'
		})
			.to(
				'.cta-subheading',
				{
					duration: 1,
					y: 0,
					opacity: 1,
					ease: 'power2.out'
				},
				'-=0.6'
			)
			.to(
				'.contact-form',
				{
					duration: 1,
					y: 0,
					opacity: 1,
					ease: 'power3.out'
				},
				'-=0.5'
			);
	}
</script>

<!-- Navigation Dots -->
<NavigationDots sections={sectionIds} {sectionNames} {activeSection} {scrollToSection} />

<!-- Video Background with Accessibility Features -->
<video
	bind:this={videoElement}
	class="fixed top-0 left-0 z-0 hidden h-full w-full object-cover transition-opacity duration-500 md:block"
	class:opacity-0={prefersReducedMotion}
	src="/background.mp4"
	muted
	loop
	autoplay
	playsinline
	aria-label="Abstract light trails background animation"
	poster="/background-poster.jpg"
>
	<source src="/background.webm" type="video/webm" />
	<source src="/background.mp4" type="video/mp4" />
</video>

<!-- Static Background Fallback for Mobile and Accessibility -->
<div
	class="fixed top-0 left-0 z-0 h-full w-full bg-gradient-to-br from-violet-900 via-purple-900 to-indigo-900 md:hidden"
></div>

<!-- Video Overlay for Content Legibility -->
<div class="fixed top-0 left-0 z-10 h-full w-full bg-black/40 backdrop-blur-[1px]"></div>

<!-- Video Controls - Only show on desktop where video is visible -->
<div class="fixed right-8 bottom-8 z-50 hidden md:block">
	<button
		onclick={toggleVideo}
		class="flex h-12 w-12 items-center justify-center rounded-full bg-white/10 text-white backdrop-blur-sm transition-all duration-300 hover:bg-white/20 focus:ring-2 focus:ring-white/50 focus:outline-none"
		aria-label={isVideoPlaying ? 'Pause background video' : 'Play background video'}
		title={isVideoPlaying ? 'Pause background video' : 'Play background video'}
	>
		{#if isVideoPlaying}
			<!-- Pause Icon -->
			<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
				<path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z" />
			</svg>
		{:else}
			<!-- Play Icon -->
			<svg class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
				<path d="M8 5v14l11-7z" />
			</svg>
		{/if}
	</button>
</div>

<!-- Hero Section -->
<section
	id="hero"
	class="relative z-20 flex h-screen w-full flex-col items-center justify-center bg-transparent p-8 text-center"
>
	<!-- Glassmorphism Container for Content -->
	<div
		class="relative rounded-3xl border border-white/10 bg-white/5 p-12 shadow-2xl backdrop-blur-md"
	>
		<div class="hero-logo">
			<Logo class="mx-auto mb-8 h-16 w-16 text-white drop-shadow-lg" />
		</div>

		<h1
			class="hero-headline font-display text-5xl font-bold tracking-tighter text-white drop-shadow-lg lg:text-7xl"
		>
			{content.hero.headline}
		</h1>

		<p
			class="hero-subheadline mx-auto mt-6 max-w-2xl text-lg text-white/90 drop-shadow-md lg:text-xl"
		>
			{content.hero.subHeadline}
		</p>

		<button
			onclick={() => scrollToSection('contact')}
			class="hero-cta mt-10 inline-block rounded-lg border border-white/30 bg-white/20 px-10 py-4 font-bold text-white backdrop-blur-sm transition-all duration-500 hover:scale-[1.02] hover:bg-white/30 focus:ring-2 focus:ring-white/50 focus:outline-none"
		>
			{content.hero.ctaText}
		</button>
	</div>
</section>

<!-- Philosophy Section -->
<section
	id="philosophy"
	class="relative z-20 flex h-screen w-full items-center justify-center bg-transparent"
>
	<!-- Section Background with Glassmorphism -->
	<div class="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>

	<div
		class="relative z-10 container mx-auto grid grid-cols-1 items-center gap-16 px-8 lg:grid-cols-2"
	>
		<!-- Left Column: Text Content -->
		<div class="rounded-2xl border border-white/10 bg-white/5 p-8 shadow-xl backdrop-blur-md">
			<h2
				class="philosophy-heading font-display text-4xl font-bold tracking-tighter text-white drop-shadow-lg lg:text-5xl"
			>
				{content.philosophy.heading}
			</h2>

			{#each content.philosophy.paragraphs as paragraph}
				<p class="philosophy-paragraph mt-6 text-lg text-white/90 drop-shadow-md">
					{paragraph}
				</p>
			{/each}
		</div>

		<!-- Right Column: Visual Element -->
		<div class="philosophy-visual flex items-center justify-center">
			<div class="relative h-80 w-80">
				<!-- Abstract animated visual -->
				<div
					class="absolute inset-0 animate-pulse rounded-full bg-gradient-to-br from-white/20 to-white/30 backdrop-blur-sm"
				></div>
				<div
					class="absolute inset-4 animate-pulse rounded-full bg-gradient-to-tl from-white/30 to-white/20 backdrop-blur-sm"
					style="animation-delay: 0.5s;"
				></div>
				<div
					class="absolute inset-8 animate-pulse rounded-full bg-white/40 backdrop-blur-sm"
					style="animation-delay: 1s;"
				></div>
			</div>
		</div>
	</div>
</section>

<!-- Services Section -->
<section
	id="services"
	class="relative z-20 flex h-screen w-full items-center justify-center bg-transparent"
>
	<!-- Section Background with Glassmorphism -->
	<div class="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>

	<div class="relative z-10 container mx-auto max-h-screen overflow-hidden px-8 pb-4">
		<!-- Header Area with Glassmorphism -->
		<div
			class="services-header mx-auto mb-8 max-w-3xl rounded-2xl border border-white/10 bg-white/5 p-8 text-center shadow-xl backdrop-blur-md"
		>
			<h2 class="text-4xl font-bold tracking-tight text-white drop-shadow-lg">
				{content.services.heading}
			</h2>
			<p class="mt-4 text-lg text-white/90 drop-shadow-md">
				{content.services.subHeading}
			</p>
		</div>

		<!-- Tab Navigation with Glassmorphism -->
		<div
			class="services-tabs mb-8 flex justify-center gap-x-4 rounded-xl border border-white/10 bg-white/5 p-2 shadow-lg backdrop-blur-md"
		>
			<button
				onclick={() => switchTab('traditional')}
				class="rounded-lg px-6 py-3 font-bold transition-all duration-300 {activeTab ===
				'traditional'
					? 'bg-white/20 text-white shadow-md'
					: 'text-white/80 hover:bg-white/10 hover:text-white'}"
			>
				{content.services.tabs.traditional.label}
			</button>
			<button
				onclick={() => switchTab('tech')}
				class="rounded-lg px-6 py-3 font-bold transition-all duration-300 {activeTab === 'tech'
					? 'bg-white/20 text-white shadow-md'
					: 'text-white/80 hover:bg-white/10 hover:text-white'}"
			>
				{content.services.tabs.tech.label}
			</button>
		</div>

		<!-- Tab Content Panels -->
		<div class="tab-content">
			{#if activeTab === 'traditional'}
				<div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
					{#each content.services.tabs.traditional.services as service}
						<div
							class="service-item rounded-xl border border-white/20 bg-white/10 p-6 shadow-xl backdrop-blur-md transition-all duration-500 hover:scale-[1.02] hover:bg-white/15 hover:shadow-2xl"
						>
							<h3 class="mb-3 text-lg font-bold text-white drop-shadow-md">
								{service.title}
							</h3>
							<p class="text-white/90 drop-shadow-sm">
								{service.description}
							</p>
						</div>
					{/each}
				</div>
			{/if}

			{#if activeTab === 'tech'}
				<div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
					{#each content.services.tabs.tech.services as service}
						<div
							class="service-item rounded-xl border border-white/20 bg-white/10 p-6 shadow-xl backdrop-blur-md transition-all duration-500 hover:scale-[1.02] hover:bg-white/15 hover:shadow-2xl"
						>
							<h3 class="mb-3 text-lg font-bold text-white drop-shadow-md">
								{service.title}
							</h3>
							<p class="text-white/90 drop-shadow-sm">
								{service.description}
							</p>
						</div>
					{/each}
				</div>
			{/if}
		</div>
	</div>
</section>

<!-- Contact Section -->
<section
	id="contact"
	class="relative z-20 flex h-screen w-full items-center justify-center bg-transparent"
>
	<!-- Section Background with Glassmorphism -->
	<div class="absolute inset-0 bg-black/20 backdrop-blur-sm"></div>

	<div class="relative z-10 container mx-auto max-h-screen overflow-y-hidden px-8">
		<!-- Combined Header and Contact Form in Single Glassmorphism Container -->
		<div class="rounded-3xl border border-white/10 bg-white/5 p-12 shadow-2xl backdrop-blur-md">
			<!-- Header -->
			<div class="mb-12 text-center">
				<h2
					class="cta-heading font-display text-4xl font-bold tracking-tighter text-white drop-shadow-lg lg:text-5xl"
				>
					{content.contact.heading}
				</h2>
				<p class="cta-subheading mx-auto mt-6 max-w-2xl text-lg text-white/90 drop-shadow-md">
					{content.contact.subHeading}
				</p>
			</div>

			<!-- Contact Form -->
			<div class="contact-form">
				<ContactForm labels={content.contact.form} />
			</div>
		</div>
	</div>
</section>
